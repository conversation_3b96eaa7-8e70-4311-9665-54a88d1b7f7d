import React, { useState } from "react";
import {
  MangaGrid,
  PopularMangaGrid,
  RecentMangaGrid,
  RecommendedMangaGrid,
  CompactMangaGrid,
} from "../components/common/MangaGrid";
import { type MangaData } from "../components/common/MangaCard";
import {
  mockMangaData,
  getPopularManga,
  getRecentManga,
  getRecommendedManga,
} from "../data/mockManga";

const MangaDemo: React.FC = () => {
  const [selectedManga, setSelectedManga] = useState<MangaData | null>(null);
  const [loading, setLoading] = useState(false);

  const handleMangaClick = (manga: MangaData) => {
    setSelectedManga(manga);
    console.log("Clicked manga:", manga);
  };

  const simulateLoading = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 space-y-12">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl lg:text-4xl font-bold mb-4">
            Manga <span className="text-[#E40066]">Collection</span>
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Discover amazing manga and comics with our responsive card layout
            component. Click on any manga to see the interaction.
          </p>

          <div className="mt-6 flex justify-center gap-4">
            <button
              onClick={simulateLoading}
              className="px-4 py-2 bg-[#E40066] text-white rounded-lg hover:bg-[#c8005a] transition-colors"
            >
              Test Loading State
            </button>
          </div>
        </div>

        {/* Selected Manga Info */}
        {selectedManga && (
          <div className="bg-white rounded-lg p-6 shadow-lg border-l-4 border-[#E40066]">
            <h3 className="text-lg font-bold mb-2">Selected Manga:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p>
                  <strong>Title:</strong> {selectedManga.title}
                </p>
                <p>
                  <strong>Author:</strong> {selectedManga.author}
                </p>
                <p>
                  <strong>Chapter:</strong> {selectedManga.chapter}
                </p>
                <p>
                  <strong>Rating:</strong> ⭐ {selectedManga.rating}
                </p>
              </div>
              <div>
                <p>
                  <strong>Status:</strong> {selectedManga.status}
                </p>
                <p>
                  <strong>Views:</strong>{" "}
                  {selectedManga.views?.toLocaleString()}
                </p>
                <p>
                  <strong>Updated:</strong> {selectedManga.updatedAt}
                </p>
                <p>
                  <strong>Genres:</strong> {selectedManga.genres.join(", ")}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Popular Manga Grid */}
        <section>
          <PopularMangaGrid
            mangas={getPopularManga()}
            onMangaClick={handleMangaClick}
            loading={loading}
            className="bg-white rounded-lg p-6 shadow-sm"
          />
        </section>

        {/* Recent Manga Grid */}
        <section>
          <RecentMangaGrid
            mangas={getRecentManga()}
            onMangaClick={handleMangaClick}
            loading={loading}
            className="bg-white rounded-lg p-6 shadow-sm"
          />
        </section>

        {/* Recommended Manga Grid */}
        <section>
          <RecommendedMangaGrid
            mangas={getRecommendedManga()}
            onMangaClick={handleMangaClick}
            loading={loading}
            className="bg-white rounded-lg p-6 shadow-sm"
          />
        </section>

        {/* Custom Grid Examples */}
        <section className="space-y-8">
          <h2 className="text-2xl font-bold text-center">
            Custom <span className="text-[#E40066]">Layouts</span>
          </h2>

          {/* Large Cards */}
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <MangaGrid
              title="Featured"
              titleHighlight="Manga"
              mangas={mockMangaData.slice(0, 6)}
              columns={{ mobile: 1, tablet: 2, desktop: 3 }}
              cardSize="lg"
              onMangaClick={handleMangaClick}
              loading={loading}
            />
          </div>

          {/* Compact Grid */}
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <CompactMangaGrid
              title="Browse"
              titleHighlight="All"
              mangas={mockMangaData}
              onMangaClick={handleMangaClick}
              loading={loading}
            />
          </div>

          {/* Without Genres */}
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <MangaGrid
              title="Simple"
              titleHighlight="Layout"
              mangas={mockMangaData.slice(0, 8)}
              showGenres={false}
              showViews={true}
              onMangaClick={handleMangaClick}
              loading={loading}
            />
          </div>
        </section>

        {/* Responsive Demo */}
        <section className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-xl font-bold mb-4">
            Responsive <span className="text-[#E40066]">Behavior</span>
          </h3>
          <div className="text-sm text-gray-600 mb-6 space-y-1">
            <p>
              📱 <strong>Mobile:</strong> 2-3 columns with smaller cards
            </p>
            <p>
              📱 <strong>Tablet:</strong> 3-4 columns with medium cards
            </p>
            <p>
              🖥️ <strong>Desktop:</strong> 4-6 columns with larger cards
            </p>
          </div>

          <MangaGrid
            mangas={mockMangaData.slice(0, 12)}
            columns={{ mobile: 2, tablet: 4, desktop: 6 }}
            cardSize="md"
            onMangaClick={handleMangaClick}
            loading={loading}
          />
        </section>

        {/* Empty State Demo */}
        <section className="bg-white rounded-lg p-6 shadow-sm">
          <MangaGrid
            title="Empty"
            titleHighlight="State"
            mangas={[]}
            onMangaClick={handleMangaClick}
          />
        </section>
      </div>
    </div>
  );
};

export default MangaDemo;

import React, { useState, useEffect, useRef } from "react";
import { Button } from "../ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "../../utils/cn";
import { SpacedButton } from "./SpacedButton";

interface HeroSlide {
  id: string;
  title: string;
  description: string;
  backgroundImage?: string;
  gradientFrom?: string;
  gradientTo?: string;
  badge?: {
    text: string;
    icon?: string;
    variant?: "default" | "success" | "warning" | "error";
  };
  categoryBadge?: {
    text: string;
    icon?: string;
    variant?: "default" | "manga" | "manhwa" | "manhua";
  };
  chapterInfo?: {
    text: string;
    onClick?: () => void;
  };
  actionButton?: {
    text: string;
    onClick?: () => void;
    variant?: "default" | "primary" | "secondary";
  };
}

interface CarouselHeroBannerProps {
  slides: HeroSlide[];
  height?: "sm" | "md" | "lg" | "xl";
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showNavigation?: boolean;
  className?: string;
  onSlideChange?: (slideIndex: number) => void;
}

const heightClasses = {
  sm: "h-64",
  md: "h-80",
  lg: "h-96",
  xl: "h-[32rem]",
};

const actionButtonVariants = {
  default: "bg-white text-gray-800 hover:bg-gray-100",
  primary: "bg-[#03CEA4] hover:bg-[#03CEA4]/90 text-white",
  secondary: "bg-gray-600 hover:bg-gray-700 text-white",
};

export const CarouselHeroBanner: React.FC<CarouselHeroBannerProps> = ({
  slides,
  height = "md",
  autoPlay = true,
  autoPlayInterval = 8000,
  showNavigation = true,
  className,
  onSlideChange,
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const touchStartX = useRef<number>(0);
  const touchEndX = useRef<number>(0);

  // Auto play functionality
  useEffect(() => {
    if (!autoPlay || slides.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, slides.length]);

  // Notify parent of slide changes
  useEffect(() => {
    onSlideChange?.(currentSlide);
  }, [currentSlide, onSlideChange]);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.targetTouches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;

    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && slides.length > 1) {
      goToNext();
    }
    if (isRightSwipe && slides.length > 1) {
      goToPrevious();
    }
  };

  if (!slides.length) return null;

  const currentSlideData = slides[currentSlide];

  const backgroundStyle = currentSlideData.backgroundImage
    ? {
        backgroundImage: `url(${currentSlideData.backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }
    : {};

  return (
    <div className={cn("w-full", className)}>
      {/* Hero Banner with container padding */}
      <div className="p-4 lg:p-6">
        <div
          className={cn(
            "relative text-white transition-all duration-300 rounded-lg overflow-hidden",
            heightClasses[height],
            currentSlideData.backgroundImage
              ? ""
              : `bg-gradient-to-r ${
                  currentSlideData.gradientFrom || "from-blue-600"
                } ${currentSlideData.gradientTo || "to-purple-600"}`
          )}
          style={backgroundStyle}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {/* Dark overlay for better text readability */}
          <div className="absolute inset-0 bg-black/40"></div>

          {/* Category badge - top right inside hero */}
          <SpacedButton className="absolute top-4 right-4" variant="secondary">
            Managa
          </SpacedButton>

          {/* Main content - center */}
          <div className="relative z-10 h-full flex items-center justify-center">
            <div className="text-center max-w-2xl px-6">
              <h1 className="text-3xl md:text-5xl font-bold mb-4 tracking-wider">
                {currentSlideData.title}
              </h1>
              <p className="text-sm md:text-lg mb-6 opacity-90">
                {currentSlideData.description}
              </p>
              {currentSlideData.actionButton && (
                <Button
                  className={cn(
                    "px-6 md:px-8 py-2 md:py-3 text-base md:text-lg rounded-lg font-semibold tracking-wider",
                    actionButtonVariants[
                      currentSlideData.actionButton.variant || "primary"
                    ]
                  )}
                  onClick={currentSlideData.actionButton.onClick}
                >
                  {currentSlideData.actionButton.text}
                </Button>
              )}
            </div>
          </div>

          {/* Chapter indicator - bottom left inside hero */}
          {currentSlideData.chapterInfo && (
            <Button
              variant="outline"
              className="absolute bottom-4 left-4 bg-white text-black border-black hover:bg-gray-100 tracking-[0.3em] uppercase font-mono text-sm"
              onClick={currentSlideData.chapterInfo.onClick}
            >
              {currentSlideData.chapterInfo.text}
            </Button>
          )}

          {/* Desktop Navigation Arrows */}
          {showNavigation && slides.length > 1 && (
            <>
              <Button
                size="icon"
                variant="secondary"
                className="absolute left-4 top-1/2 -translate-y-1/2 hidden lg:flex rounded-full bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm"
                onClick={goToPrevious}
              >
                <ChevronLeft className="w-5 h-5" />
              </Button>
              <Button
                size="icon"
                variant="secondary"
                className="absolute right-4 top-1/2 -translate-y-1/2 hidden lg:flex rounded-full bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm"
                onClick={goToNext}
              >
                <ChevronRight className="w-5 h-5" />
              </Button>
            </>
          )}

          {/* Desktop Dot Pagination */}
          {slides.length > 1 && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 hidden lg:flex gap-2">
              {slides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={cn(
                    "w-3 h-3 rounded-full transition-all duration-200",
                    index === currentSlide
                      ? "bg-white scale-110"
                      : "bg-white/50 hover:bg-white/70"
                  )}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

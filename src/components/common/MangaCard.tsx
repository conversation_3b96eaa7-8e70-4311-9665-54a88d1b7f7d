import React, { useState } from "react";
import { Card } from "../ui/card";
import { Star } from "lucide-react";
import { cn } from "../../utils/cn";

export interface MangaData {
  id: string;
  title: string;
  author: string;
  coverImage: string;
  chapter: string;
  rating: number;
  genres: string[];
  status: "Ongoing" | "Completed" | "Hiatus" | "Continuous";
  views?: number;
  updatedAt?: string;
  description?: string;
  volume?: string;
}

interface MangaCardProps {
  manga: MangaData;
  onClick?: (manga: MangaData) => void;
  className?: string;
}

export const MangaCard: React.FC<MangaCardProps> = ({
  manga,
  onClick,
  className = "",
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  const handleClick = () => {
    onClick?.(manga);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  return (
    <Card
      className={cn(
        "overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-lg group bg-white border border-gray-200 rounded-lg",
        "w-full h-[320px]", // Full width within carousel item
        className
      )}
      onClick={handleClick}
    >
      {/* Cover Image - Fixed height for consistency */}
      <div className="relative overflow-hidden h-[180px]">
        {imageLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
          </div>
        )}

        {!imageError ? (
          <img
            src={manga.coverImage}
            alt={manga.title}
            className={cn(
              "w-full h-full object-cover transition-transform duration-300 group-hover:scale-105",
              imageLoading ? "opacity-0" : "opacity-100"
            )}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
            <div className="text-gray-500 text-center">
              <div className="text-4xl mb-2">📚</div>
              <div className="text-xs">No Image</div>
            </div>
          </div>
        )}
      </div>

      {/* Blue Title Section - Matches the image */}
      <div className="bg-[#1B6FA8] text-white p-3 text-center">
        <h3
          className="font-bold text-sm leading-tight truncate"
          title={manga.title}
        >
          {manga.title}
        </h3>
        <p className="text-xs opacity-90 mt-1 truncate" title={manga.author}>
          {manga.author}
        </p>
      </div>

      {/* White Content Section */}
      <div className="bg-white p-3">
        {/* Chapter and Rating in same row */}
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium text-gray-900">
            {manga.chapter}
          </div>
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-bold text-gray-900">
              {manga.rating}
            </span>
          </div>
        </div>

        {/* Status */}
        <div className="text-xs text-gray-700 font-medium mt-2">
          {manga.status}
        </div>
      </div>
    </Card>
  );
};

import React from "react";
import { MangaCard, type MangaData } from "./MangaCard";
import { cn } from "../../utils/cn";

interface MangaGridProps {
  mangas: MangaData[];
  title?: string;
  titleHighlight?: string;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  cardSize?: "sm" | "md" | "lg";
  showGenres?: boolean;
  showViews?: boolean;
  onMangaClick?: (manga: MangaData) => void;
  className?: string;
  titleClassName?: string;
  gridClassName?: string;
  loading?: boolean;
  loadingCount?: number;
}

const getGridClasses = (columns: {
  mobile?: number;
  tablet?: number;
  desktop?: number;
}) => {
  const { mobile = 2, tablet = 3, desktop = 4 } = columns;

  // Use object mapping for better Tailwind CSS purging
  const gridColsMap: Record<number, string> = {
    1: "grid-cols-1",
    2: "grid-cols-2",
    3: "grid-cols-3",
    4: "grid-cols-4",
    5: "grid-cols-5",
    6: "grid-cols-6",
    7: "grid-cols-7",
    8: "grid-cols-8",
  };

  const smGridColsMap: Record<number, string> = {
    1: "sm:grid-cols-1",
    2: "sm:grid-cols-2",
    3: "sm:grid-cols-3",
    4: "sm:grid-cols-4",
    5: "sm:grid-cols-5",
    6: "sm:grid-cols-6",
    7: "sm:grid-cols-7",
    8: "sm:grid-cols-8",
  };

  const lgGridColsMap: Record<number, string> = {
    1: "lg:grid-cols-1",
    2: "lg:grid-cols-2",
    3: "lg:grid-cols-3",
    4: "lg:grid-cols-4",
    5: "lg:grid-cols-5",
    6: "lg:grid-cols-6",
    7: "lg:grid-cols-7",
    8: "lg:grid-cols-8",
  };

  const mobileClass = gridColsMap[mobile] || "grid-cols-2";
  const tabletClass = smGridColsMap[tablet] || "sm:grid-cols-3";
  const desktopClass = lgGridColsMap[desktop] || "lg:grid-cols-4";

  return `grid ${mobileClass} ${tabletClass} ${desktopClass}`;
};

const LoadingCard: React.FC<{ size: "sm" | "md" | "lg" }> = ({ size }) => {
  const sizeClasses = {
    sm: "h-64",
    md: "h-72 sm:h-80",
    lg: "h-80 sm:h-96",
  };

  return (
    <div
      className={cn(
        "bg-white rounded-lg overflow-hidden animate-pulse",
        sizeClasses[size]
      )}
    >
      {/* Image skeleton */}
      <div className="bg-gray-200 h-2/3"></div>

      {/* Content skeleton */}
      <div className="p-3 space-y-2">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        <div className="flex gap-1">
          <div className="h-5 bg-gray-200 rounded-full w-12"></div>
          <div className="h-5 bg-gray-200 rounded-full w-16"></div>
        </div>
        <div className="flex justify-between items-center">
          <div className="h-3 bg-gray-200 rounded w-8"></div>
          <div className="h-3 bg-gray-200 rounded w-12"></div>
        </div>
      </div>
    </div>
  );
};

export const MangaGrid: React.FC<MangaGridProps> = ({
  mangas,
  title,
  titleHighlight,
  columns = { mobile: 2, tablet: 3, desktop: 4 },
  cardSize = "md",
  showGenres = true,
  showViews = false,
  onMangaClick,
  className = "",
  titleClassName = "",
  gridClassName = "",
  loading = false,
  loadingCount = 8,
}) => {
  const renderTitle = () => {
    if (!title && !titleHighlight) return null;

    return (
      <div className="mb-6">
        <h2 className={cn("text-xl lg:text-2xl font-bold", titleClassName)}>
          {title && <span>{title}</span>}
          {titleHighlight && (
            <span className="text-[#E40066] ml-1">{titleHighlight}</span>
          )}
        </h2>
      </div>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div
          className={cn(
            getGridClasses(columns),
            "gap-4 lg:gap-6",
            gridClassName
          )}
        >
          {Array.from({ length: loadingCount }).map((_, index) => (
            <LoadingCard key={index} size={cardSize} />
          ))}
        </div>
      );
    }

    if (!mangas.length) {
      return (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📚</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No manga found
          </h3>
          <p className="text-gray-500">Try adjusting your search or filters</p>
        </div>
      );
    }

    return (
      <div
        className={cn(getGridClasses(columns), "gap-4 lg:gap-6", gridClassName)}
      >
        {mangas.map((manga) => (
          <MangaCard key={manga.id} manga={manga} onClick={onMangaClick} />
        ))}
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {renderTitle()}
      {renderContent()}
    </div>
  );
};

// Preset variants for common use cases
export const PopularMangaGrid: React.FC<
  Omit<MangaGridProps, "title" | "titleHighlight">
> = (props) => (
  <MangaGrid
    title="Popular"
    titleHighlight="Manga"
    columns={{ mobile: 2, tablet: 3, desktop: 5 }}
    cardSize="md"
    showViews={true}
    {...props}
  />
);

export const RecentMangaGrid: React.FC<
  Omit<MangaGridProps, "title" | "titleHighlight">
> = (props) => (
  <MangaGrid
    title="Recently"
    titleHighlight="Updated"
    columns={{ mobile: 2, tablet: 3, desktop: 4 }}
    cardSize="md"
    {...props}
  />
);

export const RecommendedMangaGrid: React.FC<
  Omit<MangaGridProps, "title" | "titleHighlight">
> = (props) => (
  <MangaGrid
    title="Recommended"
    titleHighlight="for You"
    columns={{ mobile: 2, tablet: 3, desktop: 6 }}
    cardSize="sm"
    {...props}
  />
);

export const CompactMangaGrid: React.FC<
  Omit<MangaGridProps, "columns" | "cardSize">
> = (props) => (
  <MangaGrid
    columns={{ mobile: 3, tablet: 4, desktop: 6 }}
    cardSize="sm"
    {...props}
  />
);

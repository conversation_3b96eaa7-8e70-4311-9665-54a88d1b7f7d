import { type MangaData } from "../components/common/MangaCard";

export const mockMangaData: MangaData[] = [
  {
    id: "1",
    title: "One Piece",
    author: "<PERSON><PERSON><PERSON><PERSON>",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 1012",
    rating: 9.36,
    genres: ["Action", "Drama", "Fantasy", "Shonen", "Adventure"],
    status: "Continuous",
    views: 2500000,
    updatedAt: "2 hours ago",
    description:
      "The story follows <PERSON>, a young man whose body gained the properties of rubber after unintentionally eating a Devil Fruit.",
  },
  {
    id: "2",
    title: "Attack on Titan",
    author: "<PERSON><PERSON><PERSON>",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 139",
    rating: 9.1,
    genres: ["Action", "Drama", "Horror", "Thriller"],
    status: "Completed",
    views: 1800000,
    updatedAt: "1 day ago",
    description: "Humanity fights for survival against giant humanoid Titans.",
  },
  {
    id: "3",
    title: "Demon Slayer",
    author: "Koyoharu Gotouge",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 205",
    rating: 8.9,
    genres: ["Action", "Supernatural", "Historical"],
    status: "Completed",
    views: 1500000,
    updatedAt: "3 days ago",
    description: "A young boy becomes a demon slayer to save his sister.",
  },
  {
    id: "4",
    title: "My Hero Academia",
    author: "Kohei Horikoshi",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 378",
    rating: 8.7,
    genres: ["Action", "School", "Superhero", "Shonen"],
    status: "Ongoing",
    views: 1200000,
    updatedAt: "5 hours ago",
    description:
      "In a world where superpowers are common, a boy without powers dreams of becoming a hero.",
  },
  {
    id: "5",
    title: "Jujutsu Kaisen",
    author: "Gege Akutami",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 245",
    rating: 8.8,
    genres: ["Action", "Supernatural", "School"],
    status: "Ongoing",
    views: 980000,
    updatedAt: "1 day ago",
    description: "Students fight cursed spirits in modern Japan.",
  },
  {
    id: "6",
    title: "Chainsaw Man",
    author: "Tatsuki Fujimoto",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 150",
    rating: 8.6,
    genres: ["Action", "Horror", "Supernatural"],
    status: "Ongoing",
    views: 850000,
    updatedAt: "6 hours ago",
    description:
      "A young man merges with a chainsaw devil to hunt other devils.",
  },
  {
    id: "7",
    title: "Tokyo Revengers",
    author: "Ken Wakui",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 278",
    rating: 8.4,
    genres: ["Action", "Drama", "Delinquents", "Time Travel"],
    status: "Completed",
    views: 720000,
    updatedAt: "2 days ago",
    description:
      "A man travels back in time to save his girlfriend and change the future.",
  },
  {
    id: "8",
    title: "Spy x Family",
    author: "Tatsuya Endo",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 95",
    rating: 9.0,
    genres: ["Comedy", "Action", "Family", "Slice of Life"],
    status: "Ongoing",
    views: 1100000,
    updatedAt: "3 hours ago",
    description:
      "A spy, an assassin, and a telepath form a fake family for their respective missions.",
  },
  {
    id: "9",
    title: "Hunter x Hunter",
    author: "Yoshihiro Togashi",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 400",
    rating: 9.2,
    genres: ["Action", "Adventure", "Fantasy"],
    status: "Hiatus",
    views: 1300000,
    updatedAt: "2 months ago",
    description:
      "A young boy searches for his father who is a legendary Hunter.",
  },
  {
    id: "10",
    title: "Naruto",
    author: "Masashi Kishimoto",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 700",
    rating: 8.5,
    genres: ["Action", "Martial Arts", "Ninja", "Shonen"],
    status: "Completed",
    views: 2000000,
    updatedAt: "5 years ago",
    description:
      "A young ninja seeks recognition and dreams of becoming the Hokage.",
  },
  {
    id: "11",
    title: "Dragon Ball Super",
    author: "Akira Toriyama",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 98",
    rating: 8.3,
    genres: ["Action", "Martial Arts", "Supernatural", "Shonen"],
    status: "Ongoing",
    views: 1600000,
    updatedAt: "1 week ago",
    description: "Goku and friends face new threats from across the universe.",
  },
  {
    id: "12",
    title: "Black Clover",
    author: "Yuki Tabata",
    coverImage:
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop",
    chapter: "Chapter 368",
    rating: 8.1,
    genres: ["Action", "Magic", "Fantasy", "Shonen"],
    status: "Ongoing",
    views: 650000,
    updatedAt: "4 days ago",
    description:
      "A boy without magic aims to become the Wizard King in a world where magic is everything.",
  },
];

export const getPopularManga = (): MangaData[] => {
  return mockMangaData
    .sort((a, b) => (b.views || 0) - (a.views || 0))
    .slice(0, 10);
};

export const getRecentManga = (): MangaData[] => {
  const timeOrder = {
    "2 hours ago": 1,
    "3 hours ago": 2,
    "5 hours ago": 3,
    "6 hours ago": 4,
    "1 day ago": 5,
    "2 days ago": 6,
    "3 days ago": 7,
    "4 days ago": 8,
    "1 week ago": 9,
    "2 months ago": 10,
    "5 years ago": 11,
  };

  return mockMangaData
    .sort((a, b) => {
      const aOrder = timeOrder[a.updatedAt as keyof typeof timeOrder] || 999;
      const bOrder = timeOrder[b.updatedAt as keyof typeof timeOrder] || 999;
      return aOrder - bOrder;
    })
    .slice(0, 8);
};

export const getRecommendedManga = (): MangaData[] => {
  return mockMangaData.sort((a, b) => b.rating - a.rating).slice(0, 12);
};

export const getMangaByGenre = (genre: string): MangaData[] => {
  return mockMangaData.filter((manga) =>
    manga.genres.some((g) => g.toLowerCase().includes(genre.toLowerCase()))
  );
};

export const searchManga = (query: string): MangaData[] => {
  const lowercaseQuery = query.toLowerCase();
  return mockMangaData.filter(
    (manga) =>
      manga.title.toLowerCase().includes(lowercaseQuery) ||
      manga.author.toLowerCase().includes(lowercaseQuery) ||
      manga.genres.some((genre) => genre.toLowerCase().includes(lowercaseQuery))
  );
};
